# User Profile API 使用说明

## 概述

新增的用户档案API允许用户管理个人资料信息，包括社交媒体链接和自定义链接。

## 接口列表

### 1. 获取用户档案信息

**接口地址：** `GET /api/user/profile`

**请求头：**
- 需要用户认证（通过session）

**响应示例：**
```json
{
  "data": {
    "bio": "我是一个开发者",
    "location": "北京",
    "website": "https://example.com",
    "socialLinks": [
      {
        "platform": "github",
        "url": "https://github.com/username"
      },
      {
        "platform": "twitter",
        "url": "https://twitter.com/username"
      },
      {
        "platform": "custom",
        "url": "https://myblog.com",
        "label": "我的博客"
      }
    ]
  }
}
```

### 2. 更新用户档案信息

**接口地址：** `PUT /api/user/profile`

**请求头：**
- `Content-Type: application/json`
- 需要用户认证（通过session）

**请求体示例：**
```json
{
  "bio": "我是一个全栈开发者，专注于Go和React",
  "location": "上海",
  "website": "https://mywebsite.com",
  "socialLinks": [
    {
      "platform": "github",
      "url": "https://github.com/myusername"
    },
    {
      "platform": "twitter",
      "url": "https://twitter.com/myusername"
    },
    {
      "platform": "linkedin",
      "url": "https://linkedin.com/in/myusername"
    },
    {
      "platform": "custom",
      "url": "https://myblog.dev",
      "label": "技术博客"
    }
  ]
}
```

**响应示例：**
```json
{
  "data": {
    "bio": "我是一个全栈开发者，专注于Go和React",
    "location": "上海",
    "website": "https://mywebsite.com",
    "socialLinks": [
      {
        "platform": "github",
        "url": "https://github.com/myusername"
      },
      {
        "platform": "twitter",
        "url": "https://twitter.com/myusername"
      },
      {
        "platform": "linkedin",
        "url": "https://linkedin.com/in/myusername"
      },
      {
        "platform": "custom",
        "url": "https://myblog.dev",
        "label": "技术博客"
      }
    ]
  }
}
```

## 数据结构说明

### UserProfile
- `bio` (string, 可选): 用户简介
- `location` (string, 可选): 用户位置
- `website` (string, 可选): 用户网站
- `socialLinks` (array, 可选): 社交媒体链接数组

### SocialLink
- `platform` (string, 必填): 平台名称，如 "github", "twitter", "linkedin", "custom"
- `url` (string, 必填): 链接地址
- `label` (string, 可选): 自定义标签，当 platform 为 "custom" 时必填

## 支持的社交平台

常见的社交平台包括：
- `github` - GitHub
- `twitter` - Twitter/X
- `linkedin` - LinkedIn
- `facebook` - Facebook
- `instagram` - Instagram
- `youtube` - YouTube
- `tiktok` - TikTok
- `custom` - 自定义链接（需要提供 label）

## 错误响应

### 400 Bad Request
```json
{
  "error": "Invalid profile data: ..."
}
```

### 401 Unauthorized
```json
{
  "error": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "error": "User not found"
}
```

## 使用示例

### JavaScript/Fetch 示例

```javascript
// 获取用户档案
async function getUserProfile() {
  const response = await fetch('/api/user/profile', {
    credentials: 'include' // 包含session cookie
  });
  const data = await response.json();
  return data;
}

// 更新用户档案
async function updateUserProfile(profileData) {
  const response = await fetch('/api/user/profile', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    credentials: 'include',
    body: JSON.stringify(profileData)
  });
  const data = await response.json();
  return data;
}
```

### cURL 示例

```bash
# 获取用户档案
curl -X GET "http://localhost:7001/api/user/profile" \
  -H "Cookie: mindelixir=your_session_cookie"

# 更新用户档案
curl -X PUT "http://localhost:7001/api/user/profile" \
  -H "Content-Type: application/json" \
  -H "Cookie: mindelixir=your_session_cookie" \
  -d '{
    "bio": "我是一个开发者",
    "location": "北京",
    "socialLinks": [
      {
        "platform": "github",
        "url": "https://github.com/username"
      }
    ]
  }'
```

## 注意事项

1. 所有字段都是可选的，可以只更新部分信息
2. 社交链接的URL必须是有效的URL格式
3. 自定义链接（platform: "custom"）必须提供label字段
4. 更新操作会完全替换现有的档案信息，而不是增量更新
5. 需要先通过OAuth登录获得有效的session
