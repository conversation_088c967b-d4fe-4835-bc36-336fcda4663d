package models

import "time"

// SocialLink represents a social media or custom link
type SocialLink struct {
	Platform string `json:"platform" bson:"platform"` // e.g., "twitter", "github", "linkedin", "custom"
	URL      string `json:"url" bson:"url"`
	Label    string `json:"label,omitempty" bson:"label,omitempty"` // For custom links
}

// UserProfile represents user's profile information
type UserProfile struct {
	Bio         string       `json:"bio,omitempty" bson:"bio,omitempty"`
	Location    string       `json:"location,omitempty" bson:"location,omitempty"`
	Website     string       `json:"website,omitempty" bson:"website,omitempty"`
	SocialLinks []SocialLink `json:"socialLinks,omitempty" bson:"socialLinks,omitempty"`
}

type User struct {
	UUID              string       `json:"uuid" bson:"uuid"`
	CreatedAt         time.Time    `json:"createdAt" bson:"createdAt"`
	UpdatedAt         time.Time    `json:"updatedAt" bson:"updatedAt"`
	Email             string       `json:"email" bson:"email"`
	Image             string       `json:"image" bson:"image"`
	Name              string       `json:"name" bson:"name"`
	Provider          string       `json:"provider" bson:"provider"`
	ProviderAccountId string       `json:"providerAccountId" bson:"providerAccountId"`
	StripeCustomerId  string       `json:"stripeCustomerId,omitempty" bson:"stripeCustomerId,omitempty"`
	Profile           *UserProfile `json:"profile,omitempty" bson:"profile,omitempty"`
}

type OriginalUserData interface{}
