package models

import "time"

type User struct {
	UUID              string    `json:"uuid" bson:"uuid"`
	CreatedAt         time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt         time.Time `json:"updatedAt" bson:"updatedAt"`
	Email             string    `json:"email" bson:"email"`
	Image             string    `json:"image" bson:"image"`
	Name              string    `json:"name" bson:"name"`
	Provider          string    `json:"provider" bson:"provider"`
	ProviderAccountId string    `json:"providerAccountId" bson:"providerAccountId"`
	StripeCustomerId  string    `json:"stripeCustomerId,omitempty" bson:"stripeCustomerId,omitempty"`
}

type OriginalUserData interface{}
