package routes

import (
	"context"
	"time"

	"github.com/SSShooter/mind-elixir-backend-go/models"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// @Summary getUserData
// @Schemes
// @Description getUserData
// @Tags user
// @Router /api/user [get]
func getUserData(userColl *mongo.Collection) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		provider := c.MustGet("p").(string)
		loginId := c.MustGet("loginId").(string)
		var result bson.M
		err := userColl.FindOne(
			context.TODO(),
			bson.D{{"uuid", provider + ":" + loginId}},
		).Decode(&result)
		if err != nil {
			c.<PERSON>(400, gin.H{"error": "no data"})
			return
		}
		c.J<PERSON>(200, gin.H{"data": result})
	}
}

// @Summary getUserProfile
// @Schemes
// @Description Get user profile information including social links
// @Tags user
// @Produce json
// @Success 200 {object} models.UserProfile
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Router /api/user/profile [get]
func getUserProfile(userColl *mongo.Collection) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		provider := c.MustGet("p").(string)
		loginId := c.MustGet("loginId").(string)
		uuid := provider + ":" + loginId

		var user models.User
		err := userColl.FindOne(
			context.TODO(),
			bson.M{"uuid": uuid},
		).Decode(&user)

		if err != nil {
			if err == mongo.ErrNoDocuments {
				c.JSON(404, gin.H{"error": "User not found"})
				return
			}
			c.JSON(400, gin.H{"error": "Failed to get user profile"})
			return
		}

		// If profile is nil, return empty profile
		if user.Profile == nil {
			user.Profile = &models.UserProfile{
				SocialLinks: []models.SocialLink{},
			}
		}

		c.JSON(200, gin.H{"data": user.Profile})
	}
}

// @Summary updateUserProfile
// @Schemes
// @Description Update user profile information including social links
// @Tags user
// @Accept json
// @Produce json
// @Param profile body models.UserProfile true "User profile data"
// @Success 200 {object} models.UserProfile
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Router /api/user/profile [put]
func updateUserProfile(userColl *mongo.Collection) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		provider := c.MustGet("p").(string)
		loginId := c.MustGet("loginId").(string)
		uuid := provider + ":" + loginId

		var profileData models.UserProfile
		if err := c.ShouldBindJSON(&profileData); err != nil {
			c.JSON(400, gin.H{"error": "Invalid profile data: " + err.Error()})
			return
		}

		// Validate social links
		for i, link := range profileData.SocialLinks {
			if link.URL == "" {
				c.JSON(400, gin.H{"error": "Social link URL cannot be empty"})
				return
			}
			if link.Platform == "" {
				c.JSON(400, gin.H{"error": "Social link platform cannot be empty"})
				return
			}
			// For custom links, require a label
			if link.Platform == "custom" && link.Label == "" {
				c.JSON(400, gin.H{"error": "Custom links must have a label"})
				return
			}
			profileData.SocialLinks[i] = link
		}

		// Update user profile
		filter := bson.M{"uuid": uuid}
		update := bson.M{
			"$set": bson.M{
				"profile":   profileData,
				"updatedAt": time.Now(),
			},
		}

		opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
		var updatedUser models.User
		err := userColl.FindOneAndUpdate(
			context.TODO(),
			filter,
			update,
			opts,
		).Decode(&updatedUser)

		if err != nil {
			if err == mongo.ErrNoDocuments {
				c.JSON(404, gin.H{"error": "User not found"})
				return
			}
			c.JSON(400, gin.H{"error": "Failed to update user profile"})
			return
		}

		c.JSON(200, gin.H{"data": updatedUser.Profile})
	}
}

func AddUserRoutes(rg *gin.RouterGroup, userColl *mongo.Collection) {
	rg.GET("", getUserData(userColl))
	rg.GET("/profile", getUserProfile(userColl))
	rg.PUT("/profile", updateUserProfile(userColl))
}
