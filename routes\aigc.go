package routes

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"

	"github.com/SSShooter/mind-elixir-backend-go/models"
	"github.com/gin-gonic/gin"
	"github.com/google/generative-ai-go/genai"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"
)

// ProxyRoundTripper is an implementation of http.RoundTripper that supports
// setting a proxy server URL for genai clients. This type should be used with
// a custom http.Client that's passed to WithHTTPClient. For such clients,
// WithAPIKey doesn't apply so the key has to be explicitly set here.
type ProxyRoundTripper struct {
	APIKey string

	ProxyURL string
}

func (t *ProxyRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	transport := http.DefaultTransport.(*http.Transport).Clone()

	if t.ProxyURL != "" {
		proxyURL, err := url.Parse(t.ProxyURL)
		if err != nil {
			return nil, err
		}
		transport.Proxy = http.ProxyURL(proxyURL)
	}

	newReq := req.Clone(req.Context())
	vals := newReq.URL.Query()
	vals.Set("key", t.APIKey)
	newReq.URL.RawQuery = vals.Encode()

	resp, err := transport.RoundTrip(newReq)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func InitGmn() *genai.Client {
	// c := &http.Client{Transport: &ProxyRoundTripper{
	// 	APIKey:   os.Getenv("AIzaSyDWIkUwc2E-QYu0OrVm7TQEmNiGLgUFsfo"),
	// 	ProxyURL: "http://localhost:2080",
	// }}

	ctx := context.Background()
	client, err := genai.NewClient(ctx, option.WithAPIKey("AIzaSyDWIkUwc2E-QYu0OrVm7TQEmNiGLgUFsfo"))
	if err != nil {
		log.Fatal(err)
	}
	// defer client.Close()

	return client
}

var schema = &genai.Schema{
	Type: genai.TypeObject,
	Properties: map[string]*genai.Schema{
		"data": {
			Type: genai.TypeArray,
			Items: &genai.Schema{
				Type: genai.TypeObject,
				Properties: map[string]*genai.Schema{
					"topic": {
						Type: genai.TypeString,
					},
				},
			},
		},
	},
}

func getPrompt(topic string, prompt string) string {
	prompt = `Given the topic '` + topic + `' and the following additional instructions (if any): ' ` + prompt + `', generate at least 6 DISTINCT and relevant sub-nodes.

	If the topic implies a sequential process or ordered steps, generate sub-nodes that reflect this order, clearly outlining the stages involved.
	
	Otherwise, explore different facets of the topic with distinct sub-nodes. Absolutely NO repetition of the main topic verbatim or with only minor wording changes is allowed in the sub-nodes. If the topic is a question, provide a concise answer and then generate sub-nodes exploring different aspects of the answer or the reasoning behind it. Each sub-node must offer a unique perspective or insight related to the main topic and contribute to a meaningful exploration or breakdown of the subject. Think laterally and creatively to ensure diverse and non-redundant sub-nodes.`
	return prompt
}

func GetAiResponse(gmn *genai.Client) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		var req *models.AiRequest
		c.ShouldBind(&req)
		prompt := getPrompt(req.Topic, req.Prompt)
		log.Println(prompt)
		log.Print(gmn)
		model := gmn.GenerativeModel("gemini-1.5-flash")

		model.ResponseMIMEType = "application/json"
		model.ResponseSchema = schema

		resp, err := model.GenerateContent(c, genai.Text(prompt))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		for _, part := range resp.Candidates[0].Content.Parts {
			if txt, ok := part.(genai.Text); ok {
				var topics map[string]interface{}
				if err := json.Unmarshal([]byte(txt), &topics); err != nil {
					log.Fatal(err)
				}
				fmt.Println(topics)
				c.JSON(http.StatusOK, gin.H{"resp": topics})
			}
		}
	}
}

func GetAiRespStream(gmn *genai.Client) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		var req *models.AiRequest
		c.ShouldBind(&req)
		prompt := getPrompt(req.Topic, req.Prompt)
		log.Println(prompt)
		log.Print(gmn)
		model := gmn.GenerativeModel("gemini-1.5-flash")

		model.ResponseMIMEType = "application/json"
		model.ResponseSchema = schema

		iter := model.GenerateContentStream(c, genai.Text(prompt))

		c.Writer.Header().Set("Content-Type", "text/event-stream")
		c.Writer.Header().Set("Cache-Control", "no-cache")
		c.Writer.Header().Set("Connection", "keep-alive")
		c.Writer.Header().Set("Transfer-Encoding", "chunked")
		c.Stream(func(w io.Writer) bool {
			resp, err := iter.Next()
			if err == iterator.Done {
				return false
			}
			if err != nil {
				log.Fatal(err)
				return false
			}

			for _, cand := range resp.Candidates {
				if cand.Content != nil {
					for _, part := range cand.Content.Parts {
						fmt.Println(part)
						c.SSEvent("message", part)
					}
				}
			}
			return true
		})

	}
}
