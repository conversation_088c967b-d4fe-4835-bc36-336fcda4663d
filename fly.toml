app = "mind-elixir-backend"
primary_region = "nrt"

[env]
BACKEND_URL = "https://mind-elixir-backend.fly.dev"
CLIENT_ID = "d833eefe517859336b47"
CLIENT_SECRET = "54a36b20e111e3b1a0999f7891ad225b8502f462"
FRONTEND_URL = "https://cloud.mind-elixir.com"
GIN_MODE = "release"
GOOGLE_CLIENT_ID = "275305206743-kqcdnpq4sa05l1heq2ut9l55gbamn3l8.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET = "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"
MONGODB_URL = "mongodb+srv://ssshooter:<EMAIL>"
PORT = "8080"
SESSION_SECRET = "ooooomindelixirhere"

[http_service]
auto_start_machines = true
auto_stop_machines = true
force_https = true
internal_port = 8_080
min_machines_running = 1
processes = [ "app" ]

[[vm]]
cpu_kind = "shared"
cpus = 1
memory_mb = 256
