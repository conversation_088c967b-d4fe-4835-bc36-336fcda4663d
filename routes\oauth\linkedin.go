package oauth

// WIP 待验证
import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/url"
	"os"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/oauth2"
)

type LinkedInUserData struct {
	Id        string `json:"id"`
	FirstName string `json:"localizedFirstName"`
	LastName  string `json:"localizedLastName"`
	Email     string `json:"emailAddress"`
	Picture   string `json:"profilePicture"`
}

func getLinkedInConf() *oauth2.Config {
	clientID := os.Getenv("LINKEDIN_CLIENT_ID")
	clientSecret := os.Getenv("LINKEDIN_CLIENT_SECRET")
	backendUrl := os.Getenv("BACKEND_URL")

	conf := &oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		RedirectURL:  backendUrl + "/oauth/linkedin/redirect",
		Scopes: []string{
			"openid",
			"profile",
			"email",
		},
		Endpoint: oauth2.Endpoint{
			AuthURL:  "https://www.linkedin.com/oauth/v2/authorization",
			TokenURL: "https://www.linkedin.com/oauth/v2/accessToken",
		},
	}
	return conf
}

func linkedinLogin(c *gin.Context) {
	query := c.Request.URL.RawQuery
	fmt.Println("query:", query)
	url := getLinkedInConf().AuthCodeURL(query)
	fmt.Println("url:", url)
	c.Redirect(301, url)
}

// @Summary linkedinAuth
// @Schemes
// @Description LinkedIn Oauth Callback
// @Param code query string true "code"
// @Tags auth
// @Success 200
// @Router /oauth/linkedin/redirect [get]
func linkedinAuth(userColl *mongo.Collection) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get custom parameters
		customParam, _ := c.GetQuery("state")
		values, _ := url.ParseQuery(customParam)
		port := values.Get("port")
		loginType := values.Get("type")
		fmt.Println("Port:", port, "Type:", loginType)

		var code string
		conf := getLinkedInConf()
		code, _ = c.GetQuery("code")
		// Handle the exchange code to initiate a transport.
		tok, err := conf.Exchange(oauth2.NoContext, code)
		if err != nil {
			c.JSON(400, err.Error())
			return
		}

		client := conf.Client(oauth2.NoContext, tok)
		fmt.Println("client!")

		// Get user profile data
		resp, err := client.Get("https://api.linkedin.com/v2/me?projection=(id,localizedFirstName,localizedLastName)")
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}

		defer resp.Body.Close()
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return
		}

		var profileData map[string]interface{}
		err = json.Unmarshal(body, &profileData)
		if err != nil {
			fmt.Println("Error parsing profile data:", err)
			return
		}

		// Get email address
		emailResp, err := client.Get("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))")
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}

		defer emailResp.Body.Close()
		emailBody, err := ioutil.ReadAll(emailResp.Body)
		if err != nil {
			return
		}

		var emailData map[string]interface{}
		err = json.Unmarshal(emailBody, &emailData)
		if err != nil {
			fmt.Println("Error parsing email data:", err)
			return
		}

		// Extract email from the response
		var email string
		if elements, ok := emailData["elements"].([]interface{}); ok && len(elements) > 0 {
			if element, ok := elements[0].(map[string]interface{}); ok {
				if handle, ok := element["handle~"].(map[string]interface{}); ok {
					if emailValue, ok := handle["emailAddress"].(string); ok {
						email = emailValue
					}
				}
			}
		}

		// Combine data
		data := LinkedInUserData{
			Id:        profileData["id"].(string),
			FirstName: profileData["localizedFirstName"].(string),
			LastName:  profileData["localizedLastName"].(string),
			Email:     email,
		}

		fmt.Println("data", data)

		session := sessions.Default(c)
		session.Set("loginId", data.Id)
		session.Save()
		err = updateUserData(userColl, data)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		frontendUrl := os.Getenv("FRONTEND_URL")
		if loginType == "desktop" {
			c.Redirect(301, frontendUrl+"/#/desktop?port="+port)
		} else {
			c.Redirect(301, frontendUrl)
		}
	}
}
