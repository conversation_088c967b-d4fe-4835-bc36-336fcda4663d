package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/SSShooter/mind-elixir-backend-go/docs"
	"github.com/SSShooter/mind-elixir-backend-go/middlewares"
	"github.com/SSShooter/mind-elixir-backend-go/routes"
	"github.com/SSShooter/mind-elixir-backend-go/routes/oauth"
	"github.com/SSShooter/mind-elixir-backend-go/utils"
)

func connectDatabase() (*mongo.Client, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	url := os.Getenv("MONGODB_URL")
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(url))
	if err != nil {
		return nil, err
	}
	return client, nil
}

// @Summary login
// @Schemes
// @Description It will redirect to GitHub login page
// @Tags auth
// @Success 200
// @Router /login [get]

// @Summary logout
// @Schemes
// @Description Clear session
// @Tags auth
// @Success 200
// @Router /logout [get]
func main() {
	err := godotenv.Load()
	if err != nil {
		fmt.Println("Error loading .env file")
	}
	client, err := connectDatabase()
	db := client.Database("test")
	mapColl := db.Collection("maps")
	deskPayDb := client.Database("mind_elixir")
	newUserColl := deskPayDb.Collection("users")
	if err != nil {
		log.Fatal("Db connect fail")
	}

	r := gin.Default()

	// 添加打印请求header的中间件，测试用
	// r.Use(middlewares.LogHeaders())

	gmn := routes.InitGmn()
	defer gmn.Close()
	r.POST("/me-ai", routes.GetAiResponse(gmn))
	r.POST("/me-ai-stream", routes.GetAiRespStream(gmn))

	docs.SwaggerInfo.BasePath = "/"
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))

	sessionSecret := os.Getenv("SESSION_SECRET")
	store := cookie.NewStore([]byte(sessionSecret))
	store.Options(sessions.Options{
		MaxAge:   60 * 60 * 24 * 7,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteNoneMode,
	})
	r.Use(sessions.Sessions("mindelixir", store))

	// AllowOrigin := os.Getenv("FRONTEND_URL")
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://tauri.localhost", os.Getenv("FRONTEND_URL")},
		AllowMethods:     []string{"GET", "POST", "DELETE", "PUT", "PATCH"},
		AllowHeaders:     []string{"Origin", "content-type"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	oauthGroup := r.Group("/oauth")
	oauth.AddOauthRoutes(oauthGroup, newUserColl)

	api := r.Group("/api")
	api.Use(middlewares.Auth())
	user := api.Group("/user")
	routes.AddUserRoutes(user, newUserColl)
	mapr := api.Group("/map")
	routes.AddMapRoutes(mapr, mapColl)

	// no auth
	public := r.Group("/api/public")
	routes.AddPublicMapRoutes(public, mapColl)

	r.GET("/api/desktop-user", func(c *gin.Context) {
		// 查订阅表并加密传到client
		session := sessions.Default(c)
		provider := session.Get("p")
		loginId := session.Get("loginId")
		uuid := provider.(string) + ":" + loginId.(string)
		col := newUserColl
		var result bson.M
		err := col.FindOne(
			context.TODO(),
			bson.M{"uuid": uuid},
			options.FindOne().SetProjection(bson.M{
				"email": 1,
				"type":  1,
			}),
		).Decode(&result)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		result["exp"] = time.Now().AddDate(0, 1, 0).Unix()
		// Fix: utils.EncryptData expects ([]byte, []byte), returns (string, error)
		// Marshal result to JSON bytes
		resultBytes, err := json.Marshal(result)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to marshal user data"})
			return
		}
		// 生成 AES-GCM 的 32 字节密钥（示例：实际应从安全来源获取或配置）
		key := []byte("c12a45f78cabddefa143233687a0c1e2") // 32字节密钥
		encrypted, err := utils.EncryptData(key, resultBytes)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to encrypt user data"})
			return
		}
		c.JSON(200, gin.H{"data": encrypted})
	})

	r.GET("/token", func(c *gin.Context) {
		token, _ := c.Cookie("mindelixir")
		c.JSON(200, gin.H{"token": token})
	})
	r.GET("/logout", func(c *gin.Context) {
		session := sessions.Default(c)
		session.Set("loginId", nil)
		session.Set("p", nil)
		session.Save()
		c.JSON(200, gin.H{"msg": "logout"})
	})
	port := os.Getenv("PORT")
	if port == "" {
		port = "7001"
	}
	r.Run(":" + port) // 写 127 flyio 部署会有问题
}
