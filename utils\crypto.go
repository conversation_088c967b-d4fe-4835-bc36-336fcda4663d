package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
)

// encryptData 使用 AES-GCM 加密数据
func EncryptData(key []byte, plaintext []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("创建 cipher 失败: %w", err)
	}

	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("创建 GCM 失败: %w", err)
	}

	// 生成 Nonce。Nonce 的长度对于 AES-GCM 应该是标准的 12 字节。
	// 对于 GCM，Nonce 不需要保密，但每次加密都必须是唯一的。
	nonce := make([]byte, aesgcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("生成 Nonce 失败: %w", err)
	}

	// Seal 会将 Nonce 作为前缀附加到密文中，然后进行加密和认证。
	// 第一个参数是 dst，如果为 nil，它会自动分配。
	// 第二个参数是 nonce。
	// 第三个参数是 plaintext。
	// 第四个参数是 additionalData (可选的附加认证数据，这里为 nil)。
	ciphertext := aesgcm.Seal(nil, nonce, plaintext, nil)

	// 将 nonce 和 ciphertext 拼接起来
	// 通常的做法是将 nonce 放在密文的前面
	encryptedData := append(nonce, ciphertext...)

	// 使用 Base64 编码以便传输
	return base64.StdEncoding.EncodeToString(encryptedData), nil
}
