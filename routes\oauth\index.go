package oauth

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/SSShooter/mind-elixir-backend-go/models"
)

func ConvertToUser(u interface{}) (models.User, error) {
	switch v := u.(type) {
	case GoogleUserData:
		return models.User{
			Email:             v.Email,
			Image:             v.Picture,
			Name:              v.Name,
			Provider:          "google",
			ProviderAccountId: v.Id,
		}, nil
	case GithubUserdata:
		return models.User{
			Email:             v.Email,
			Image:             v.AvatarUrl,
			Name:              v.Name,
			Provider:          "github",
			ProviderAccountId: strconv.Itoa(v.Id),
		}, nil
	case LinkedInUserData:
		return models.User{
			Email:             v.Email,
			Image:             v.Picture,
			Name:              v.FirstName + " " + v.LastName,
			Provider:          "linkedin",
			ProviderAccountId: v.Id,
		}, nil
	default:
		return models.User{}, fmt.Errorf("unsupported user type")
	}
}

func updateUserData(coll *mongo.Collection, origin interface{}) error {
	data, _ := ConvertToUser(origin)
	opts := options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)
	filter := bson.M{"uuid": data.Provider + ":" + data.ProviderAccountId}

	// 准备更新数据
	setOnInsert := bson.M{
		"createdAt":         time.Now(),
		"providerAccountId": data.ProviderAccountId,
		"provider":          data.Provider,
	}

	// 始终更新这些字段
	set := bson.M{
		"updatedAt": time.Now(),
		"email":     data.Email,
		"image":     data.Image,
		"name":      data.Name,
	}

	// 构造更新操作
	update := bson.M{
		"$set":         set,
		"$setOnInsert": setOnInsert,
	}

	// 执行更新并获取结果
	var updatedDocument bson.M
	err := coll.FindOneAndUpdate(
		context.TODO(),
		filter,
		update,
		opts,
	).Decode(&updatedDocument)

	// 忽略文档不存在的错误，因为我们使用了upsert
	if err != nil && err != mongo.ErrNoDocuments {
		return err
	}
	return nil
}

func AddOauthRoutes(rg *gin.RouterGroup, userColl *mongo.Collection) {
	rg.GET("/github/login", githubLogin) // 可传 type/port
	rg.GET("/github/redirect", githubAuth(userColl))
	rg.GET("/google/login", googleLogin)
	rg.GET("/google/redirect", googleAuth(userColl))
	rg.GET("/linkedin/login", linkedinLogin)
	rg.GET("/linkedin/redirect", linkedinAuth(userColl))
}
