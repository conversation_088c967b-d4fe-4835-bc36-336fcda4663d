package middlewares

import (
	"fmt"
	"log"

	"github.com/gin-gonic/gin"
)

// LogHeaders 中间件用于打印每个请求的header信息
func LogHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 打印请求方法和URL
		log.Printf("请求: %s %s\n", c.Request.Method, c.Request.URL.Path)
		
		// 打印所有header
		log.Println("请求头信息:")
		for key, values := range c.Request.Header {
			for _, value := range values {
				log.Printf("  %s: %s\n", key, value)
			}
		}
		
		// 继续处理请求
		c.Next()
		
		// 打印响应状态码
		log.Printf("响应状态码: %d\n", c.Writer.Status())
		fmt.Println() // 添加一个空行，使日志更易读
	}
}
